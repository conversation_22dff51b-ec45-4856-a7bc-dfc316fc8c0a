<template>
  <div class="h-full flex flex-col bg-gray-50">
    <!-- 页面标题和进度指示器 -->
    <div class="bg-white border-b px-6 py-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">排产规划工作台</h1>
          <p class="text-gray-600 mt-1">智能排产 + 异步切割优化 + 最终确认</p>
        </div>

        <!-- 阶段进度指示器 -->
        <SchedulingPhaseManager
          :current-phase="schedulingStore.currentPhase"
          @phase-change="handlePhaseChange"
        />
      </div>
    </div>

    <!-- 主工作区域 - 两栏布局 -->
    <div class="flex-1 flex gap-6 p-6 min-h-0">
      <!-- 主要工作区域 -->
      <div class="flex-1 bg-white rounded-lg border overflow-hidden flex flex-col">
        <!-- 工作区头部 - 包含标题和操作按钮 -->
        <div class="p-4 border-b">
          <div class="flex items-center justify-between">
            <div>
              <h2 class="text-lg font-semibold">{{ getMainAreaTitle() }}</h2>
              <p class="text-sm text-gray-500">{{ getMainAreaDescription() }}</p>
            </div>

            <!-- 操作按钮组 -->
            <div class="flex items-center space-x-2">
              <!-- 阶段特定控制按钮 -->
              <Button
                v-if="schedulingStore.currentPhase === 'pre-scheduling'"
                variant="default"
                size="sm"
                @click="showSchedulingWizard = true"
                class="flex items-center space-x-2"
              >
                <Play class="h-4 w-4" />
                <span>排产向导</span>
                <Badge v-if="schedulingStore.selectedBatches.length > 0" variant="secondary" class="ml-1">
                  {{ schedulingStore.selectedBatches.length }}
                </Badge>
              </Button>

              <Button
                v-else-if="schedulingStore.currentPhase === 'cutting-optimization'"
                variant="default"
                size="sm"
                @click="showCuttingOptimizationDialog = true"
              >
                <Scissors class="h-4 w-4 mr-2" />
                切割优化
              </Button>

              <Button
                v-else-if="schedulingStore.currentPhase === 'final-confirmation'"
                variant="default"
                size="sm"
                @click="showFinalConfirmationDialog = true"
              >
                <CheckCircle class="h-4 w-4 mr-2" />
                最终确认
              </Button>
            </div>
          </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 overflow-hidden">
          <!-- 预排产视图 -->
          <PreSchedulingView
            v-if="schedulingStore.currentPhase === 'pre-scheduling'"
            :pre-schedule="schedulingStore.preSchedule"
            :loading="schedulingStore.isScheduling"
            @task-click="handleTaskClick"
            @batch-details="handleBatchDetails"
            @confirm-schedule="handleConfirmSchedule"
            @regenerate-schedule="handleRegenerateSchedule"
            @export-schedule="handleExportSchedule"
          />

          <!-- 切割优化阶段 - 显示预排产结果 -->
          <div v-else-if="schedulingStore.currentPhase === 'cutting-optimization'" class="p-6">
            <PreScheduleResultDisplay
              :selected-batches="schedulingStore.selectedBatches"
              :estimated-duration="schedulingStore.preSchedule?.estimatedMetrics?.totalDuration || 0"
              :equipment-utilization="schedulingStore.preSchedule?.estimatedMetrics?.equipmentUtilization || 0"
              :estimated-cost="calculateEstimatedCost()"
              @batch-details="handleBatchDetails"
            />
          </div>

          <!-- 最终确认视图 -->
          <FinalConfirmationView
            v-else-if="schedulingStore.currentPhase === 'final-confirmation'"
            :final-schedule="schedulingStore.finalSchedule"
            :comparison="schedulingStore.comparisonData"
          />
        </div>
      </div>

      <!-- 右侧：详情面板 -->
      <div class="w-80 bg-white rounded-lg border overflow-hidden flex flex-col">
        <div class="p-4 border-b">
          <h3 class="font-semibold">详情信息</h3>
        </div>

        <div class="flex-1 overflow-y-auto p-4">
          <SchedulingDetailsPanel
            :current-phase="schedulingStore.currentPhase"
            :selected-batches="schedulingStore.selectedBatches"
            :metrics="schedulingStore.currentMetrics"
          />
        </div>
      </div>
    </div>

    <!-- 模态对话框 -->
    <!-- 排产向导对话框 -->
    <Dialog v-model:open="showSchedulingWizard">
      <DialogContent class="w-5xl h-[90vh] p-0">
        <SchedulingWizard
          :batches="schedulingStore.availableBatches"
          :loading="schedulingStore.loading"
          :initial-selected-ids="schedulingStore.selectedBatches.map(b => b.id)"
          :is-scheduling="schedulingStore.isScheduling"
          @batch-select="handleBatchSelect"
          @start-scheduling="handleStartSchedulingFromWizard"
          @close="showSchedulingWizard = false"
        />
      </DialogContent>
    </Dialog>

    <!-- 切割优化控制对话框 -->
    <CuttingOptimizationDialog
      :is-open="showCuttingOptimizationDialog"
      :current-step="getCurrentOptimizationStep()"
      :export-status="schedulingStore.exportStatus"
      :import-status="schedulingStore.importStatus"
      :export-data="schedulingStore.cuttingExport"
      :improvements="schedulingStore.cuttingResult?.improvements"
      :batch-count="schedulingStore.selectedBatches.length"
      :estimated-time="30"
      @close="showCuttingOptimizationDialog = false"
      @export-data="handleExportData"
      @download-file="handleDownloadFile"
      @import-result="handleImportResult"
      @simulate-complete="handleSimulateComplete"
      @view-results="handleViewResults"
      @proceed-to-final="handleProceedToFinal"
    />

    <!-- 最终确认控制对话框 -->
    <Dialog v-model:open="showFinalConfirmationDialog">
      <DialogContent class="max-w-3xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>最终确认</DialogTitle>
          <DialogDescription>
            查看优化结果对比，确认最终排产方案
          </DialogDescription>
        </DialogHeader>
        <div class="max-h-[60vh] overflow-y-auto">
          <FinalConfirmationControls
            :comparison-data="schedulingStore.comparisonData"
            @confirm-schedule="handleConfirmSchedule"
          />
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { Play, Scissors, CheckCircle } from 'lucide-vue-next';
import { useSchedulingStore } from '@/stores/schedulingStore';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import SchedulingPhaseManager from '@/components/mes/scheduling/SchedulingPhaseManager.vue';
import SchedulingWizard from '@/components/mes/scheduling/SchedulingWizard.vue';
import PreSchedulingView from '@/components/mes/scheduling/PreSchedulingView.vue';
import CuttingOptimizationDialog from '@/components/mes/scheduling/CuttingOptimizationDialog.vue';
import PreScheduleResultDisplay from '@/components/mes/scheduling/PreScheduleResultDisplay.vue';
import FinalConfirmationView from '@/components/mes/scheduling/FinalConfirmationView.vue';
import FinalConfirmationControls from '@/components/mes/scheduling/FinalConfirmationControls.vue';
import SchedulingDetailsPanel from '@/components/mes/scheduling/SchedulingDetailsPanel.vue';

// Store
const schedulingStore = useSchedulingStore();

// 模态对话框状态
const showSchedulingWizard = ref(false);
const showCuttingOptimizationDialog = ref(false);
const showFinalConfirmationDialog = ref(false);

// 生命周期
onMounted(async () => {
  console.log('排产规划工作台已加载');
  await schedulingStore.initializeWorkbench();
});

// 事件处理
const handlePhaseChange = (phase: string) => {
  schedulingStore.setCurrentPhase(phase as any);
};

const handleBatchSelect = (batchIds: string[]) => {
  schedulingStore.selectBatches(batchIds);
};

const handleStartSchedulingFromWizard = async () => {
  await schedulingStore.startPreScheduling();
  // 向导会在排产完成后自动关闭
};

const handleExportData = async () => {
  await schedulingStore.exportCuttingData();
};

const handleImportResult = async (file: File) => {
  await schedulingStore.importCuttingResult(file);
};

const handleApproveResult = async () => {
  // 批准切割优化结果
  console.log('批准切割优化结果');
  // 这里可以添加具体的批准逻辑
  schedulingStore.setCurrentPhase('final-confirmation');
};

const handleRejectResult = async (reason: string) => {
  // 拒绝切割优化结果
  console.log('拒绝切割优化结果:', reason);
  // 这里可以添加具体的拒绝逻辑，比如重新导出数据
};

const handleProceedToFinal = async () => {
  // 进入最终确认阶段
  schedulingStore.setCurrentPhase('final-confirmation');
  showCuttingOptimizationDialog.value = false;
};

const handleDownloadFile = () => {
  // 下载导出文件
  if (schedulingStore.cuttingExport) {
    // 这里实现文件下载逻辑
    const filename = `cutting_data_${schedulingStore.cuttingExport.exportId}.xlsx`;
    // 模拟下载
    console.log('下载文件:', filename);
  }
};

const handleViewResults = () => {
  // 查看详细结果
  showCuttingOptimizationDialog.value = false;
  // 可以打开一个详细的结果查看器
  console.log('查看切割优化结果详情');
};

const handleSimulateComplete = async () => {
  // 模拟切割优化完成
  try {
    await schedulingStore.simulateCuttingOptimizationComplete();
  } catch (error) {
    console.error('模拟切割优化完成失败:', error);
  }
};

const getCurrentOptimizationStep = () => {
  if (schedulingStore.cuttingResult) {
    return 'completed';
  } else if (schedulingStore.cuttingExport && schedulingStore.importStatus === 'waiting') {
    return 'waiting';
  } else if (schedulingStore.cuttingExport && schedulingStore.importStatus === 'importing') {
    return 'import';
  } else if (schedulingStore.cuttingExport) {
    return 'import';
  } else {
    return 'export';
  }
};

const calculateEstimatedCost = (): number => {
  // 计算预估成本
  return schedulingStore.selectedBatches.reduce((total: number, batch: any) => {
    return total + batch.items.reduce((batchTotal: number, item: any) => {
      const area = (item.specifications.length * item.specifications.width) / 1000000; // 转换为平方米
      const cost = area * item.selectedQuantity * 50; // 假设每平方米50元
      return batchTotal + cost;
    }, 0);
  }, 0);
};

const handleConfirmSchedule = async () => {
  if (schedulingStore.currentPhase === 'pre-scheduling') {
    await schedulingStore.confirmPreSchedule();
  } else {
    await schedulingStore.confirmFinalSchedule();
  }
};

// 甘特图事件处理
const handleTaskClick = (taskId: string) => {
  console.log('任务点击:', taskId);
  // TODO: 显示任务详情或编辑对话框
};

const handleBatchDetails = (batch: any) => {
  console.log('批次详情:', batch);
  // TODO: 显示批次详情对话框
};

const handleRegenerateSchedule = async () => {
  console.log('重新生成排产方案');
  await schedulingStore.startPreScheduling();
};

const handleExportSchedule = () => {
  console.log('导出排产方案');
  // TODO: 实现排产方案导出功能
};

// 计算属性
const getMainAreaTitle = () => {
  switch (schedulingStore.currentPhase) {
    case 'pre-scheduling':
      return '智能预排产';
    case 'cutting-optimization':
      return '切割优化进行中';
    case 'final-confirmation':
      return '最终排产确认';
    default:
      return '排产规划';
  }
};

const getMainAreaDescription = () => {
  switch (schedulingStore.currentPhase) {
    case 'pre-scheduling':
      return '基于标准工时和设备产能进行初步排产';
    case 'cutting-optimization':
      return '等待第三方切割优化系统完成数据处理';
    case 'final-confirmation':
      return '基于切割优化结果确认最终排产方案';
    default:
      return '选择批次开始排产规划';
  }
};
</script>
