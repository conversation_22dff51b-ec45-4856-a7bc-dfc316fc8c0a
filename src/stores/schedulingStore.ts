import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type {
  OptimizedBatch,
  PreScheduleResult,
  CuttingExportData,
  CuttingImportResult,
  FinalScheduleResult,
  SchedulingPhase,
  SchedulingMetrics,
  GanttViewConfiguration
} from '@/types/scheduling';
import { preSchedulingService } from '@/services/preSchedulingService';
import { cuttingDataExportService } from '@/services/cuttingDataExportService';
import { cuttingResultImportService } from '@/services/cuttingResultImportService';
import { finalSchedulingService } from '@/services/finalSchedulingService';
import { DataIntegrityService } from '@/services/dataIntegrityService';
import { useMesStore } from '@/stores/mesStore';

export const useSchedulingStore = defineStore('scheduling', () => {
  // 基础状态
  const loading = ref(false);
  const error = ref<string | null>(null);

  // 阶段管理
  const currentPhase = ref<SchedulingPhase>('pre-scheduling');

  // 甘特图视图配置（固定为工艺段视图）
  const ganttViewConfig = ref<GanttViewConfiguration>({
    resourceOrganization: 'process_segment',
    timeGranularity: 'hour',
    showUtilization: true,
    showBottlenecks: true,
    enableDrillDown: true
  });
  
  // 批次数据
  const availableBatches = ref<OptimizedBatch[]>([]);
  const selectedBatches = ref<OptimizedBatch[]>([]);
  
  // 预排产阶段
  const preSchedule = ref<PreScheduleResult | null>(null);
  const isScheduling = ref(false);
  
  // 切割优化阶段
  const cuttingExport = ref<CuttingExportData | null>(null);
  const exportStatus = ref<'idle' | 'exporting' | 'exported' | 'error'>('idle');
  const importStatus = ref<'waiting' | 'importing' | 'imported' | 'error'>('waiting');
  const cuttingResult = ref<CuttingImportResult | null>(null);
  
  // 最终确认阶段
  const finalSchedule = ref<FinalScheduleResult | null>(null);
  const comparisonData = ref<any>(null);

  // 数据完整性状态
  const dataIntegrityReport = ref<any>(null);
  const lastIntegrityCheck = ref<string | null>(null);
  
  // 计算属性
  const currentMetrics = computed((): SchedulingMetrics => {
    switch (currentPhase.value) {
      case 'pre-scheduling':
        return preSchedule.value?.estimatedMetrics || {
          totalDuration: 0,
          equipmentUtilization: 0,
          deliveryAchievement: 0,
          materialUtilization: 0
        };
      case 'final-confirmation':
        return finalSchedule.value?.finalMetrics || {
          totalDuration: 0,
          equipmentUtilization: 0,
          deliveryAchievement: 0,
          materialUtilization: 0
        };
      default:
        return {
          totalDuration: 0,
          equipmentUtilization: 0,
          deliveryAchievement: 0,
          materialUtilization: 0
        };
    }
  });
  
  const canProceedToNext = computed(() => {
    switch (currentPhase.value) {
      case 'pre-scheduling':
        return preSchedule.value !== null && selectedBatches.value.length > 0;
      case 'cutting-optimization':
        return cuttingResult.value !== null && importStatus.value === 'imported';
      case 'final-confirmation':
        return finalSchedule.value !== null;
      default:
        return false;
    }
  });

  // Actions
  const initializeWorkbench = async () => {
    loading.value = true;
    error.value = null;
    
    try {
      // 从生产工单系统获取可用批次
      const mesStore = useMesStore();
      await mesStore.loadProductionOrders();
      
      // 转换为排产批次格式
      availableBatches.value = await convertProductionOrdersToBatches(mesStore.productionOrders);

      // 验证数据完整性
      await validateDataIntegrity();

      console.log(`已加载 ${availableBatches.value.length} 个可排产批次`);
    } catch (err) {
      error.value = '初始化工作台失败: ' + (err as Error).message;
      console.error('初始化工作台失败:', err);
    } finally {
      loading.value = false;
    }
  };

  const setCurrentPhase = (phase: SchedulingPhase) => {
    currentPhase.value = phase;
    console.log(`切换到阶段: ${phase}`);
  };

  const selectBatches = (batchIds: string[]) => {
    selectedBatches.value = availableBatches.value.filter(batch => 
      batchIds.includes(batch.id)
    );
    console.log(`已选择 ${selectedBatches.value.length} 个批次`);
  };

  const startPreScheduling = async () => {
    if (selectedBatches.value.length === 0) {
      error.value = '请先选择要排产的批次';
      return;
    }

    isScheduling.value = true;
    error.value = null;

    try {
      console.log('开始预排产计算...');
      preSchedule.value = await preSchedulingService.generatePreSchedule(
        selectedBatches.value,
        ganttViewConfig.value
      );
      
      console.log('预排产完成:', preSchedule.value);
    } catch (err) {
      error.value = '预排产失败: ' + (err as Error).message;
      console.error('预排产失败:', err);
    } finally {
      isScheduling.value = false;
    }
  };

  const exportCuttingData = async () => {
    if (!preSchedule.value) {
      error.value = '请先完成预排产';
      return;
    }

    exportStatus.value = 'exporting';
    error.value = null;

    try {
      console.log('开始导出切割数据...');
      cuttingExport.value = await cuttingDataExportService.exportCuttingData(selectedBatches.value);
      exportStatus.value = 'exported';
      
      // 自动切换到切割优化阶段
      currentPhase.value = 'cutting-optimization';
      
      console.log('切割数据导出完成');
    } catch (err) {
      exportStatus.value = 'error';
      error.value = '导出切割数据失败: ' + (err as Error).message;
      console.error('导出失败:', err);
    }
  };

  const importCuttingResult = async (file: File) => {
    importStatus.value = 'importing';
    error.value = null;

    try {
      console.log('开始导入切割结果...');
      cuttingResult.value = await cuttingResultImportService.importCuttingResult(file);
      importStatus.value = 'imported';

      // 生成对比数据
      comparisonData.value = generateComparisonData();

      // 自动切换到最终确认阶段并生成最终方案
      currentPhase.value = 'final-confirmation';
      await generateFinalSchedule();

      console.log('切割结果导入完成');
    } catch (err) {
      importStatus.value = 'error';
      error.value = '导入切割结果失败: ' + (err as Error).message;
      console.error('导入失败:', err);
    }
  };

  /**
   * 模拟切割优化完成（用于原型演示）
   */
  async function simulateCuttingOptimizationComplete() {
    if (importStatus.value !== 'waiting') {
      console.log('切割优化已完成或正在进行中');
      return;
    }

    importStatus.value = 'importing';
    error.value = null;

    try {
      console.log('模拟第三方切割优化系统处理完成...');
      cuttingResult.value = await cuttingResultImportService.simulateOptimizationComplete();
      importStatus.value = 'imported';

      // 自动进入最终确认阶段并生成最终方案
      setCurrentPhase('final-confirmation');

      console.log('模拟切割优化完成，已进入最终确认阶段');
    } catch (err) {
      importStatus.value = 'error';
      error.value = '模拟切割优化失败: ' + (err as Error).message;
      console.error('模拟失败:', err);
    }
  }

  const generateFinalSchedule = async () => {
    if (!preSchedule.value || !cuttingResult.value) {
      error.value = '缺少预排产或切割优化结果，无法生成最终方案';
      console.error(error.value);
      return;
    }

    loading.value = true;
    error.value = null;

    try {
      console.log('开始重构计划，生成最终排产方案...');
      finalSchedule.value = await finalSchedulingService.generateFinalSchedule(
        preSchedule.value,
        cuttingResult.value
      );

      console.log('最终排产方案已生成:', finalSchedule.value);

      // 同时生成用于UI展示的对比数据
      comparisonData.value = finalSchedulingService.generateComparisonData(
        preSchedule.value,
        finalSchedule.value
      );
      console.log('对比数据已生成:', comparisonData.value);

    } catch (err) {
      error.value = '生成最终排产方案失败: ' + (err as Error).message;
      console.error('最终排产失败:', err);
    } finally {
      loading.value = false;
    }
  };

  const confirmPreSchedule = async () => {
    if (!preSchedule.value) {
      error.value = '没有预排产结果可确认';
      return;
    }

    loading.value = true;
    error.value = null;

    try {
      console.log('确认预排产方案，进入切割优化阶段...');

      // 导出切割数据
      await exportCuttingData();

      // 切换到切割优化阶段
      setCurrentPhase('cutting-optimization');

      console.log('已进入切割优化阶段');
    } catch (err) {
      error.value = '确认预排产方案失败: ' + (err as Error).message;
      console.error('确认预排产失败:', err);
    } finally {
      loading.value = false;
    }
  };

  const confirmFinalSchedule = async () => {
    if (!finalSchedule.value) {
      error.value = '没有可供提交的最终排产方案';
      return;
    }

    loading.value = true;
    error.value = null;

    try {
      console.log('正在提交最终排产方案到服务器...');
      const success = await finalSchedulingService.submitFinalSchedule(finalSchedule.value);

      if (success) {
        console.log('排产方案提交成功！');
        // 可以在这里触发一个全局通知
        // 然后重置工作台
        resetWorkbench();
        // 并可能导航到另一个页面，如生产监控仪表盘
      } else {
        throw new Error('服务器返回提交失败');
      }
    } catch (err) {
      error.value = '提交最终排产方案失败: ' + (err as Error).message;
      console.error('提交最终排产方案失败:', err);
    } finally {
      loading.value = false;
    }
  };

  // 辅助函数
  const convertProductionOrdersToBatches = async (productionOrders: any[]): Promise<OptimizedBatch[]> => {
    // 从生产工单转换为排产批次，保持完整的外键关联
    return productionOrders.map((order, index) => {
      // 计算总数量
      const totalQuantity = order.items?.reduce((sum: number, item: any) => sum + item.quantity, 0) || 0;

      // 从工艺流程计算预估时间
      const estimatedTime = order.items?.[0]?.processFlow?.reduce((sum: number, step: any) =>
        sum + (step.estimatedDuration || 0), 0) || 480;

      // 从计划日期推导优先级
      const priority = derivePriorityFromOrder(order);

      // 获取主要工作站 - 优先显示关键工艺段
      const primaryWorkstation = getPrimaryWorkstation(order.items?.[0]?.processFlow || []);

      // 转换订单项为选中订单项格式
      const convertedItems = order.items?.map((item: any) => ({
        id: item.customerOrderItemId || item.id,
        customerOrderId: order.customerOrderId,
        orderNumber: order.customerOrderNumber,
        customerName: order.customerName,
        specifications: item.specifications,
        totalQuantity: item.quantity,
        selectedQuantity: item.quantity,
        processFlow: item.processFlow || [],
        deliveryDate: order.plannedEndDate || '2024-03-01',
        originalItem: item,
        // 保持外键关联
        productionOrderId: order.id,
        productionOrderItemId: item.id
      })) || [];

      return {
        id: `batch_${order.workOrderNumber || order.id}`, // 使用工单号作为批次ID基础
        name: `${order.customerName}-批次${index + 1}`,
        items: convertedItems,
        processFlow: order.items?.[0]?.processFlow || [],
        workstation: primaryWorkstation,
        workstationGroup: getWorkstationGroup(primaryWorkstation),
        totalQuantity,
        estimatedTime,
        utilization: calculateUtilization(order),
        priority,
        conflictLevel: 'none' as const,
        // 保持与原工单的外键关联
        sourceProductionOrderId: order.id,
        sourceWorkOrderNumber: order.workOrderNumber,
        sourceCustomerOrderId: order.customerOrderId,
        sourceCustomerOrderNumber: order.customerOrderNumber
      };
    });
  };

  // 辅助函数：从工单推导优先级
  const derivePriorityFromOrder = (order: any): 'urgent' | 'high' | 'normal' | 'low' => {
    // 基于计划开始日期和状态推导优先级
    const plannedStart = new Date(order.plannedStartDate || Date.now());
    const now = new Date();
    const daysUntilStart = Math.ceil((plannedStart.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    if (order.priority) {
      return order.priority; // 如果工单已有优先级，直接使用
    }

    if (daysUntilStart <= 1) return 'urgent';
    if (daysUntilStart <= 3) return 'high';
    if (daysUntilStart <= 7) return 'normal';
    return 'low';
  };

  // 辅助函数：获取工作站组
  const getWorkstationGroup = (workstation: string): string => {
    const workstationGroups: Record<string, string> = {
      'cold_processing': '冷工段',
      'hot_processing': '热工段',
      'cutting': '切割组',
      'edging': '磨边组',
      'tempering': '钢化工段',
      'laminating': '合片工段',
      'insulating': '合片工段',
      'bending': '热弯工段',
      'coating': '镀膜工段',
      'special_processing': '特殊工段',
      'quality_control': '质检工段',
      'packaging': '包装工段'
    };
    return workstationGroups[workstation] || '冷工段';
  };

  // 辅助函数：获取主要工作站 - 优先显示关键工艺段
  const getPrimaryWorkstation = (processFlow: any[]): string => {
    if (!processFlow || processFlow.length === 0) return 'cold_processing';

    // 工艺段优先级：特殊工艺 > 热工艺 > 冷工艺
    const workstationPriority: Record<string, number> = {
      'bending': 10,        // 热弯 - 最高优先级
      'coating': 9,         // 镀膜
      'tempering': 8,       // 钢化
      'laminating': 7,      // 夹胶
      'insulating': 6,      // 中空
      'special_processing': 5, // 特殊处理
      'quality_control': 4, // 质检
      'packaging': 3,       // 包装
      'cold_processing': 2  // 冷工段 - 基础工艺
    };

    // 找到优先级最高的工作站
    let primaryWorkstation = 'cold_processing';
    let maxPriority = 0;

    for (const step of processFlow) {
      const priority = workstationPriority[step.workstation] || 1;
      if (priority > maxPriority) {
        maxPriority = priority;
        primaryWorkstation = step.workstation;
      }
    }

    return primaryWorkstation;
  };

  // 辅助函数：计算设备利用率
  const calculateUtilization = (order: any): number => {
    // 基于工单复杂度和数量计算利用率
    const baseUtilization = 75;
    const quantityFactor = Math.min(order.items?.length || 1, 5) * 2; // 项目数量影响
    const complexityFactor = order.items?.[0]?.processFlow?.length || 1; // 工艺复杂度影响

    return Math.min(95, baseUtilization + quantityFactor + complexityFactor);
  };

  const generateComparisonData = () => {
    if (!preSchedule.value || !cuttingResult.value) return null;
    
    return {
      materialUsage: {
        estimated: 45,
        actual: 42,
        improvement: -3
      },
      utilization: {
        estimated: 85,
        actual: 89,
        improvement: 4
      },
      duration: {
        estimated: 8.5,
        actual: 7.2,
        improvement: -1.3
      },
      totalDuration: {
        estimated: 5.5,
        actual: 5.1,
        improvement: -0.4
      }
    };
  };

  // 数据完整性验证
  const validateDataIntegrity = async () => {
    try {
      const mesStore = useMesStore();
      const report = DataIntegrityService.validateBatchIntegrity(
        availableBatches.value,
        mesStore.productionOrders
      );

      dataIntegrityReport.value = report;
      lastIntegrityCheck.value = new Date().toISOString();

      if (!report.isValid) {
        console.warn('数据完整性验证发现问题:', report.summary);
        console.table(report.issues);

        // 尝试自动修复
        availableBatches.value = DataIntegrityService.repairDataRelationships(
          availableBatches.value,
          mesStore.productionOrders
        );

        console.log('已尝试自动修复数据关联性问题');
      } else {
        console.log('✅ 数据完整性验证通过:', report.summary);
      }
    } catch (err) {
      console.error('数据完整性验证失败:', err);
    }
  };

  const getDataIntegrityStatus = () => {
    if (!dataIntegrityReport.value) return null;

    const report = dataIntegrityReport.value;
    const relationshipReport = DataIntegrityService.generateRelationshipReport(availableBatches.value);

    return {
      ...report,
      relationshipReport,
      lastCheck: lastIntegrityCheck.value
    };
  };

  const resetWorkbench = () => {
    currentPhase.value = 'pre-scheduling';
    selectedBatches.value = [];
    preSchedule.value = null;
    cuttingExport.value = null;
    cuttingResult.value = null;
    finalSchedule.value = null;
    comparisonData.value = null;
    exportStatus.value = 'idle';
    importStatus.value = 'waiting';
    dataIntegrityReport.value = null;
    lastIntegrityCheck.value = null;
    error.value = null;
  };

  // 甘特图视图配置管理
  const updateGanttViewConfig = (config: Partial<GanttViewConfiguration>) => {
    ganttViewConfig.value = { ...ganttViewConfig.value, ...config };
  };

  // 资源组织方式固定为工艺段视图
  const setResourceOrganization = () => {
    // 固定为工艺段视图，不再支持切换
    ganttViewConfig.value.resourceOrganization = 'process_segment';
  };

  return {
    // State
    loading,
    error,
    currentPhase,
    availableBatches,
    selectedBatches,
    preSchedule,
    isScheduling,
    cuttingExport,
    exportStatus,
    importStatus,
    cuttingResult,
    finalSchedule,
    comparisonData,
    ganttViewConfig,

    // Computed
    currentMetrics,
    canProceedToNext,

    // Actions
    initializeWorkbench,
    setCurrentPhase,
    selectBatches,
    startPreScheduling,
    exportCuttingData,
    importCuttingResult,
    simulateCuttingOptimizationComplete,
    confirmPreSchedule,
    generateFinalSchedule,
    confirmFinalSchedule,
    validateDataIntegrity,
    getDataIntegrityStatus,
    resetWorkbench,
    updateGanttViewConfig,
    setResourceOrganization
  };
});
