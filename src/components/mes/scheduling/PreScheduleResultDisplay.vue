<template>
  <div class="bg-white rounded-lg border p-6">
    <!-- 标题 -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h3 class="text-lg font-semibold text-gray-900">预排产结果概览</h3>
        <p class="text-sm text-gray-600 mt-1">确认要进行切割优化的批次和排产方案</p>
      </div>
      <Badge variant="outline" class="text-blue-700 border-blue-300">
        已完成预排产
      </Badge>
    </div>

    <!-- 关键指标 -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
      <div class="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div class="text-2xl font-bold text-blue-600">{{ selectedBatches.length }}</div>
        <div class="text-sm text-blue-700">已选择批次</div>
      </div>
      <div class="text-center p-4 bg-green-50 rounded-lg border border-green-200">
        <div class="text-2xl font-bold text-green-600">{{ formatDuration(estimatedDuration) }}</div>
        <div class="text-sm text-green-700">计划工期</div>
      </div>
      <div class="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
        <div class="text-2xl font-bold text-purple-600">{{ equipmentUtilization.toPrecision(4) }}%</div>
        <div class="text-sm text-purple-700">设备利用率</div>
      </div>
      <div class="text-center p-4 bg-orange-50 rounded-lg border border-orange-200">
        <div class="text-2xl font-bold text-orange-600">¥{{ formatCurrency(estimatedCost) }}</div>
        <div class="text-sm text-orange-700">预估成本</div>
      </div>
    </div>

    <!-- 批次详情 -->
    <div class="mb-6">
      <h4 class="text-sm font-medium text-gray-900 mb-3">批次详情</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div 
          v-for="batch in selectedBatches" 
          :key="batch.id"
          class="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
          @click="showBatchDetails(batch)"
        >
          <div class="flex items-center justify-between mb-2">
            <div class="font-medium text-gray-900">{{ getCustomerText(batch) }}</div>
            <Badge variant="outline" :class="getPriorityBadgeClass(batch.priority)">
              {{ getPriorityText(batch.priority) }}
            </Badge>
          </div>
          
          <div class="space-y-1 text-sm text-gray-600">
            <div class="flex justify-between">
              <span>总面积:</span>
              <span class="font-medium">{{ calculateTotalArea(batch) }} ㎡</span>
            </div>
            <div class="flex justify-between">
              <span>产品数:</span>
              <span class="font-medium">{{ batch.items.length }} 项</span>
            </div>
            <div class="flex justify-between">
              <span>主要规格:</span>
              <span class="font-medium">{{ getMainSpecification(batch) }}</span>
            </div>
            <div class="flex justify-between">
              <span>交期:</span>
              <span class="font-medium">{{ getEarliestDeliveryDate(batch) }}</span>
            </div>
          </div>
          
          <!-- 进度条 -->
          <div class="mt-3">
            <div class="flex justify-between text-xs text-gray-500 mb-1">
              <span>排产进度</span>
              <span>{{ batch.schedulingProgress || 0 }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-1.5">
              <div 
                class="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                :style="{ width: `${batch.schedulingProgress || 0}%` }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 优化目标 -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-start space-x-3">
        <Target class="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
        <div>
          <div class="text-sm font-medium text-blue-900 mb-1">切割优化目标</div>
          <div class="text-sm text-blue-700 space-y-1">
            <div>• 提升原片利用率至85%以上</div>
            <div>• 降低切割成本10-15%</div>
            <div>• 减少废料产生，提高材料利用效率</div>
            <div>• 优化切割顺序，缩短生产周期</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作提示 -->
    <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
      <div class="flex items-start space-x-3">
        <Info class="h-5 w-5 text-yellow-600 flex-shrink-0 mt-0.5" />
        <div class="text-sm text-yellow-800">
          <div class="font-medium mb-1">下一步操作</div>
          <div>点击右上角"切割优化"按钮，开始数据导出和第三方系统优化流程</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Target, Info } from 'lucide-vue-next';
import { Badge } from '@/components/ui/badge';
import type { OptimizedBatch } from '@/types/scheduling';

interface Props {
  selectedBatches: OptimizedBatch[];
  estimatedDuration: number; // 小时
  equipmentUtilization: number; // 百分比
  estimatedCost: number; // 元
}

interface Emits {
  (e: 'batch-details', batch: OptimizedBatch): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

// 计算函数
const calculateTotalArea = (batch: OptimizedBatch) => {
  return batch.items.reduce((total, item) => {
    const area = (item.specifications.length * item.specifications.width) / 1000000; // 转换为平方米
    return total + (area * item.selectedQuantity);
  }, 0).toFixed(1);
};

const getMainSpecification = (batch: OptimizedBatch) => {
  const firstItem = batch.items[0];
  if (!firstItem) return '--';

  const { thickness } = firstItem.specifications;
  const processType = firstItem.processFlow?.[0]?.stepName || '普通';
  return `${thickness}mm ${processType}`;
};

const getPriorityText = (priority: string) => {
  const priorityMap = {
    urgent: '紧急',
    high: '高',
    normal: '普通',
    low: '低'
  };
  return priorityMap[priority as keyof typeof priorityMap] || '普通';
};

const getPriorityBadgeClass = (priority: string) => {
  const classMap = {
    urgent: 'text-red-700 border-red-300',
    high: 'text-orange-700 border-orange-300',
    normal: 'text-blue-700 border-blue-300',
    low: 'text-gray-700 border-gray-300'
  };
  return classMap[priority as keyof typeof classMap] || 'text-gray-700 border-gray-300';
};

// 工具函数
const formatDuration = (hours: number) => {
  const days = Math.floor(hours / 24);
  const remainingHours = hours % 24;
  
  if (days > 0) {
    return `${days}.${Math.round(remainingHours / 24 * 10).toPrecision(2)}天`;
  } else {
    return `${hours.toPrecision(2)}小时`;
  }
};

const formatCurrency = (amount: number) => {
  return amount.toLocaleString('zh-CN');
};

const getEarliestDeliveryDate = (batch: OptimizedBatch) => {
  if (!batch.items || batch.items.length === 0) return '--';

  // 找到最早的交期
  const earliestDate = batch.items.reduce((earliest, item) => {
    if (!item.deliveryDate) return earliest;
    const itemDate = new Date(item.deliveryDate);
    if (isNaN(itemDate.getTime())) return earliest;

    if (!earliest) return itemDate;
    return itemDate < earliest ? itemDate : earliest;
  }, null as Date | null);

  if (!earliestDate) return '--';
  return earliestDate.toLocaleDateString('zh-CN');
};

const getCustomerText = (batch: OptimizedBatch) => {
  const customers = [...new Set(batch.items.map(item => item.customerName))];

  if (customers.length === 1) {
    return customers[0];
  } else if (customers.length <= 3) {
    return customers.join(', ');
  } else {
    return `${customers.slice(0, 2).join(', ')} 等${customers.length}家`;
  }
};

const showBatchDetails = (batch: OptimizedBatch) => {
  emit('batch-details', batch);
};
</script>
